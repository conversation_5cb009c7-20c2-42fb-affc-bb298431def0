# auto-wechat — 自动化关心助手（Java + Python wxauto）

简体中文说明。

## 功能
- Java 定时任务 + 调用 Python 脚本（wxauto）控制 PC 微信发送消息
- 支持多联系人、多时间段、变量替换（{date}、{weather}）、随机每日一句
- 运行环境：Windows（需 PC 微信已登录），安装 Python + wxauto

## 快速开始
1. 下载并解压本工程。
2. 安装 Python（推荐 3.8+），然后在工程 `scripts` 目录运行：
   ```bash
   pip install -r scripts/requirements.txt
   ```
3. 用文本编辑器修改 `src/main/resources/config.json`，配置联系人、时间、城市等。
4. 启动方法（两种）：
   - 直接运行 jar（Maven 打包）：
     ```bash
     mvn package
     java -jar target/auto-wechat-1.0-SNAPSHOT.jar
     ```
   - 开发运行：在 IDE（IntelliJ/Eclipse）中运行 `com.example.autowechat.AutoWeChat`。
5. 请确保 PC 微信已登录并且处于可用状态。wxauto 会尝试激活微信窗口并搜索联系人。

## 目录结构（已包含）
- `pom.xml` — Maven 配置
- `src/main/java/.../AutoWeChat.java` — 主程序
- `src/main/java/.../HttpUtils.java` — 简单 HTTP 工具
- `src/main/resources/config.json` — 配置文件（请编辑）
- `resources/messages.txt` — 随机每日句（可扩展）
- `scripts/send_wechat.py` — Python 发送脚本（使用 wxauto）
- `scripts/requirements.txt` — Python 依赖列表

## 注意事项与风险提示
- 本工程使用非官方自动化方法控制个人微信（wxauto），请确保用途合规，避免频繁、骚扰性质的自动发送以降低账号风险。
- 运行时需保证程序所在电脑处于登录状态并可操作微信窗口。
- 如果希望更稳定、合规的方案，请使用企业微信 API（官方）。

如果需要我把工程里某些功能扩展（如图片、表情、更多消息模板、GUI 管理界面），告诉我具体需求即可。
