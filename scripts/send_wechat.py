# send_wechat.py
# 使用 wxauto 控制 PC 微信（仅支持 Windows）
# 安装: pip install wxauto pywin32
import sys
import time
from wxauto import WeChat

def main():
    if len(sys.argv) < 3:
        print("usage: send_wechat.py <friend_name> <message>")
        return
    friend = sys.argv[1]
    message = sys.argv[2]
    # 如果 message 含有空格或特殊字符，最好通过 Java 进行正确的转义或传参
    wx = WeChat()
    # 有时需要等待微信启动或界面响应
    time.sleep(0.5)
    try:
        wx.ChatWith(friend)
        time.sleep(0.3)
        wx.SendMsg(message)
        print(f"ok: sent to {friend}")
    except Exception as e:
        print("error:", e)

if __name__ == '__main__':
    main()
