package com.example.autowechat;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class AutoWeChat {
    private static JSONObject config;
    private static final String PYTHON = "python"; // or full path to python.exe
    private static final String SCRIPT = "scripts/send_wechat.py";

    public static void main(String[] args) throws Exception {
        loadConfig();
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(4);

        JSONArray schedule = config.getJSONArray("schedule");
        for (int i = 0; i < schedule.length(); i++) {
            JSONObject job = schedule.getJSONObject(i);
            String time = job.getString("time"); // HH:mm
            Runnable task = () -> {
                try {
                    sendJob(job);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            };
            long initial = getDelaySeconds(time);
            scheduler.scheduleAtFixedRate(task, initial, 24 * 3600, TimeUnit.SECONDS);
            System.out.println("Scheduled job at " + time + " (initial delay " + initial + "s)"); 
        }
    }

    private static void loadConfig() throws IOException {
        Path p = Path.of("src/main/resources/config.json");
        String s = Files.readString(p, StandardCharsets.UTF_8);
        config = new JSONObject(s);
    }

    private static void sendJob(JSONObject job) throws IOException, InterruptedException {
        String template = job.getString("message");
        String city = config.optString("weatherCity", ""); 
        String date = java.time.LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        String weather = (city.isEmpty() ? "未知" : HttpUtils.getWeather(city));
        String finalMsg = template.replace("{date}", date).replace("{weather}", weather);

        // Optionally support random message from resources/messages.txt when template == "{random}"
        if ("{random}".equals(template)) {
            finalMsg = pickRandomMessage();
        }

        JSONArray friends = config.getJSONArray("friends");
        for (int i = 0; i < friends.length(); i++) {
            String friend = friends.getString(i);
            sendToFriend(friend, finalMsg);
        }
    }

    private static void sendToFriend(String friend, String msg) throws IOException {
        ProcessBuilder pb = new ProcessBuilder(PYTHON, SCRIPT, friend, msg);
        pb.inheritIO();
        Process p = pb.start();
        try {
            int code = p.waitFor();
            System.out.println("Sent to " + friend + ", exit=" + code);
        } catch (InterruptedException e) {
            p.destroy();
            Thread.currentThread().interrupt();
        }
    }

    private static long getDelaySeconds(String timeStr) {
        LocalTime now = LocalTime.now();
        LocalTime target = LocalTime.parse(timeStr);
        long diff = Duration.between(now, target).getSeconds();
        if (diff < 0) diff += 24 * 3600;
        return diff;
    }

    private static String pickRandomMessage() {
        try {
            var lines = Files.readAllLines(Path.of("resources/messages.txt"), StandardCharsets.UTF_8);
            var rnd = new Random();
            if (lines.isEmpty()) return "想你了~";
            return lines.get(rnd.nextInt(lines.size()));
        } catch (IOException e) {
            return "想你了~";
        }
    }
}
