package com.example.autowechat;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class HttpUtils {
    // 简单从 wttr.in 获取天气（注意：依赖网络）
    public static String getWeather(String city) {
        try {
            String url = "https://wttr.in/" + java.net.URLEncoder.encode(city, StandardCharsets.UTF_8) + "?format=%C%20%t";
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                String line = br.readLine();
                return line == null ? "未知" : line;
            }
        } catch (Exception e) {
            return "未知";
        }
    }
}
